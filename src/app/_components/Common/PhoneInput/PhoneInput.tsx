'use client';

import { countries, Country } from '@/src/app/_utils/countries';
import { formatPhoneNumber } from '@/src/app/_utils/phoneFormat';
import { cn } from '@/src/app/_utils/utils';
import { formatPhoneInput, validatePhoneNumber } from '@/src/app/_utils/validation/phoneValidation';
import React, { useEffect, useState } from 'react';
import ReactCountryFlag from 'react-country-flag';

interface PhoneInputProps {
  /**
   * Current phone number value
   */
  value: string | undefined;

  /**
   * Callback function when value changes
   */
  onChange: (value: string) => void;

  /**
   * Placeholder text for the input field
   */
  placeholder?: string;

  /**
   * Callback function when validation error occurs
   */
  onValidationError?: (error: string | null) => void;

  /**
   * Additional CSS classes to apply to the component
   */
  className?: string;

  /**
   * Whether to use the formatPhoneNumber function from phoneFormat.ts (true)
   * or the formatPhoneInput function from phoneValidation.ts (false)
   * Default: false (use formatPhoneInput)
   */
  useFormatPhoneNumber?: boolean;

  /**
   * Whether to show the country code dropdown
   * Default: true
   */
  showCountryCode?: boolean;

  /**
   * Grid column classes for responsive layout
   * Default: empty string (no grid classes)
   */
  gridColumnClasses?: string;

  /**
   * Function to trigger form validation after value change
   */
  triggerValidation?: () => void;

  /**
   * Country code for the phone number
   * Default: '+55' (Brazil)
   * @deprecated Use showCountryCode=false instead of passing a different country code
   */
  countryCode?: string;
}

/**
 * Phone input component with country code display
 * Unified implementation that works across the application
 */
export const PhoneInput: React.FC<PhoneInputProps> = ({
  value = '',
  onChange,
  placeholder = '(00) 00000-0000',
  onValidationError,
  className = '',
  useFormatPhoneNumber = false,
  showCountryCode = true,
  gridColumnClasses = '',
  triggerValidation,
  countryCode = '+55', // For backward compatibility
}) => {
  const [error, setError] = useState<string | null>(null);
  const [hasInteracted, setHasInteracted] = useState(false);

  // Always use Brazil (+55) as the country code
  const effectiveCountryCode = '+55'; // Force Brazil regardless of passed countryCode
  const brazilCountry = countries.find((c: Country) => c.code === effectiveCountryCode);

  // Validate the phone number whenever the value changes
  useEffect(() => {
    if (hasInteracted) {
      validateInput(value);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, hasInteracted]);

  /**
   * Validates the phone input and updates error state
   * @param inputValue The current input value
   * @returns True if the input is valid, false otherwise
   */
  const validateInput = (inputValue: string): boolean => {
    // Use the centralized validation utility
    const validationResult = validatePhoneNumber(inputValue, effectiveCountryCode);

    // Update error state if it has changed
    if (validationResult.error !== error) {
      setError(validationResult.error);
      if (onValidationError) {
        onValidationError(validationResult.error);
      }
    }

    return validationResult.isValid;
  };

  const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    setHasInteracted(true);
    validateInput(event.target.value);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    let formattedValue: string;

    // Extract digits only
    const digits = inputValue.replace(/\D/g, '');

    // Get the current value's digits (to check if we're trying to add more digits)
    const currentDigits = (value || '').replace(/\D/g, '');

    // For Brazilian numbers, limit to 11 digits
    if (effectiveCountryCode === '+55') {
      // If the current value already has 11 digits and the user is trying to add more digits
      // (i.e., the new input has more digits than the current value), prevent the change
      if (currentDigits.length === 11 && digits.length > 11) {
        // Just return without updating the value
        return;
      }

      // If the user is trying to enter more than 11 digits in a single operation
      // (e.g., pasting a long number), truncate it but don't show an error
      if (digits.length > 11) {
        // Use only the first 11 digits
        const truncatedDigits = digits.substring(0, 11);

        // Format the truncated digits
        if (useFormatPhoneNumber) {
          formattedValue = formatPhoneNumber(truncatedDigits, effectiveCountryCode);
        } else {
          formattedValue = formatPhoneInput(truncatedDigits, effectiveCountryCode);
        }

        // Clear any previous error if the truncated value is valid
        if (truncatedDigits.length === 11) {
          setError(null);
          if (onValidationError) {
            onValidationError(null);
          }
        } else if (hasInteracted) {
          // Only validate if the user has interacted with the field
          validateInput(formattedValue);
        }
      } else {
        // Format the phone number based on the specified formatter
        if (useFormatPhoneNumber) {
          formattedValue = formatPhoneNumber(digits, effectiveCountryCode);
        } else {
          formattedValue = formatPhoneInput(digits, effectiveCountryCode);
        }

        // Validate in real-time as the user types
        if (hasInteracted) {
          validateInput(formattedValue);
        }
      }
    } else {
      // For non-Brazilian numbers, use the standard formatters
      if (useFormatPhoneNumber) {
        formattedValue = formatPhoneNumber(digits, effectiveCountryCode);
      } else {
        formattedValue = formatPhoneInput(inputValue, effectiveCountryCode);
      }
    }

    onChange(formattedValue);
    setHasInteracted(true);

    // Trigger form validation if provided
    if (triggerValidation) {
      triggerValidation();
    }
  };

  // Determine the input field classes based on whether country code is shown
  const inputFieldClasses = cn(
    'h-10 w-full border border-input bg-background p-2 px-3 py-2 text-base ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
    showCountryCode ? 'rounded-r-md border-l-0' : 'rounded-md',
    error ? 'focus:ring-black-500 border-red-500' : 'border-gray-200 focus:ring-blue-500'
  );

  return (
    <div className={cn('flex flex-col', gridColumnClasses, className)}>
      <div className="flex w-full">
        {/* Country code dropdown (non-functional, Brazil only) */}
        {showCountryCode && brazilCountry && (
          <div className="flex h-10 w-[90px] items-center rounded-l-md border border-r-0 border-input bg-background px-3 py-2 text-sm ring-offset-background">
            <span className="flex items-center">
              <ReactCountryFlag
                countryCode={brazilCountry.iso}
                svg
                style={{
                  width: '1.5em',
                  height: '1.5em',
                  marginRight: '0.5em',
                }}
              />
              {brazilCountry.code}
            </span>
          </div>
        )}

        {/* Phone input field */}
        <input
          type="tel"
          placeholder={placeholder}
          value={value || ''}
          onChange={handleChange}
          onBlur={handleBlur}
          className={inputFieldClasses}
          aria-label="Seu número de telefone"
        />
      </div>

      {/* Error message */}
      {error && <span className="mt-1 text-sm font-medium text-red-500">{error}</span>}
    </div>
  );
};

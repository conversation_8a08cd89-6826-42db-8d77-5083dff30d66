'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  ErrorDisplay,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  OTPInput,
  PhoneInput,
  Separator,
} from '@/src/app/_components';
import { useAuth } from '@/src/app/_context/AuthContext';
import { usePhoneVerification } from '@/src/app/_hooks';
import { cn, formatPhoneNumber, validatePhoneNumber } from '@/src/app/_utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { AnimatePresence, motion } from 'framer-motion';
import { ArrowLeft, Loader2, ShieldCheckIcon } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

// Form validation schema for phone step
const phoneVerificationSchema = z.object({
  phone: z
    .string()
    .min(1, 'Insira um número de telefone válido com DDD.')
    .refine(
      (value) => {
        const { isValid } = validatePhoneNumber(value, '+55');
        return isValid;
      },
      {
        message: 'Insira um número de telefone válido com DDD.',
      }
    ),
});

type PhoneVerificationFormValues = z.infer<typeof phoneVerificationSchema>;

interface AuthenticationFormProps {
  className?: string;
}

export function AuthenticationForm({ className }: AuthenticationFormProps) {
  const [step, setStep] = useState<'phone' | 'otp'>('phone');
  const router = useRouter();
  const {
    sendVerificationCode,
    verifyCode,
    isLoading: isPhoneVerificationLoading,
    error: phoneVerificationError,
    phoneNumber,
    resetVerification,
  } = usePhoneVerification();
  const { auth } = useAuth();

  const [phoneInputError, setPhoneInputError] = useState<string | null>(null);
  const [otpValue, setOtpValue] = useState('');
  const [formError, setFormError] = useState<string | null>(null); // Combined error state

  // Initialize form for phone step
  const phoneForm = useForm<PhoneVerificationFormValues>({
    resolver: zodResolver(phoneVerificationSchema),
    defaultValues: {
      phone: '',
    },
  });

  // Auto-validate OTP when all digits are filled
  useEffect(() => {
    if (step === 'otp' && otpValue.length === 6) {
      handleVerifyOTP();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [otpValue, step]);

  // Handle phone form submission
  const onSubmitPhone = async (values: PhoneVerificationFormValues) => {
    try {
      setFormError(null);
      setPhoneInputError(null);

      const validationResult = validatePhoneNumber(values.phone, '+55');
      if (!validationResult.isValid) {
        setPhoneInputError(validationResult.error);
        return;
      }

      const success = await sendVerificationCode(values.phone);
      if (success) {
        setStep('otp');
      }
    } catch (err) {
      console.error('Error submitting phone verification form:', err);
      setFormError(err instanceof Error ? err.message : 'Erro ao enviar código de verificação');
    }
  };

  // Handle OTP verification
  const handleVerifyOTP = async () => {
    if (otpValue.length === 6) {
      setFormError(null);
      const success = await verifyCode(otpValue);
      if (success) {
        router.push('/agendamentos');
      }
    }
  };

  // Handle resend code
  const handleResendCode = async () => {
    setFormError(null);
    setOtpValue('');
    if (phoneNumber) {
      // Attempt to resend code for the current phoneNumber
      // This assumes sendVerificationCode can be called again for resend
      const success = await sendVerificationCode(phoneNumber);
      if (!success) {
        setFormError('Erro ao reenviar o código. Tente novamente.');
      }
    } else {
      // If phoneNumber is somehow not available, prompt to go back
      setFormError('Número de telefone não encontrado. Por favor, volte e tente novamente.');
    }
  };

  // Handle going back to phone verification step
  const handleBack = () => {
    setFormError(null);
    setStep('phone');
    setOtpValue('');
    resetVerification(); // Resets phoneNumber in usePhoneVerification hook
    phoneForm.reset(); // Reset the phone form as well
  };

  const currentError = formError || phoneVerificationError || phoneInputError;
  const isLoading = isPhoneVerificationLoading;

  // If user is already authenticated, show a message
  if (auth.isAuthenticated) {
    return (
      <div className={cn('mx-auto w-full max-w-md text-center', className)}>
        <h2 className="mb-4 text-xl font-bold">Você já está autenticado</h2>
        <p className="text-gray-600">Você já está logado no sistema.</p>
      </div>
    );
  }

  const formattedPhoneForOTP = formatPhoneNumber(phoneNumber, '+55');

  return (
    <div className={cn('mx-auto w-full max-w-md', className)}>
      <Card className="w-full p-8 shadow-xl">
        <AnimatePresence mode="wait">
          <motion.div
            key={step}
            initial={{ opacity: 0, x: step === 'phone' ? -20 : 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: step === 'phone' ? 20 : -20 }}
            transition={{ duration: 0.3 }}
            className="flex h-full flex-col"
          >
            {step === 'phone' ? (
              <>
                <Form {...phoneForm}>
                  <form
                    onSubmit={phoneForm.handleSubmit(onSubmitPhone)}
                    className="flex h-full flex-col"
                  >
                    <CardHeader>
                      <CardTitle className="text-4xl font-bold leading-tight">Entrar</CardTitle>
                      <CardDescription className="text-base">
                        Acesse seus agendamentos já realizados com seu número de celular.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-grow space-y-8">
                      <FormField
                        control={phoneForm.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={phoneInputError ? 'text-red-500' : ''}>
                              Celular
                            </FormLabel>
                            <FormControl>
                              <PhoneInput
                                value={field.value}
                                placeholder="(00) 00000-0000"
                                onChange={(value: string) => {
                                  field.onChange(value);
                                  phoneForm.trigger('phone');
                                  setPhoneInputError(null); // Clear manual error on change
                                }}
                                onValidationError={setPhoneInputError} // For specific phone input errors
                                triggerValidation={() => phoneForm.trigger('phone')}
                              />
                            </FormControl>
                            {!phoneInputError && <FormMessage />}
                            {/* Display react-hook-form error if no custom phoneInputError */}
                          </FormItem>
                        )}
                      />
                      {currentError && !phoneInputError && (
                        <ErrorDisplay message={currentError} fullPage={false} />
                      )}
                    </CardContent>
                    <CardFooter className="mt-auto flex flex-col space-y-4">
                      <Button
                        type="submit"
                        className="w-full rounded-xl"
                        disabled={isLoading || !!phoneInputError || !phoneForm.formState.isValid}
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Enviando...
                          </>
                        ) : (
                          'Enviar código de verificação'
                        )}
                      </Button>
                      <Separator />
                      <div className="mt-8 flex w-full flex-col items-start gap-2 text-sm">
                        <Link href="/#" className="text-primary hover:underline">
                          Mudei de número de celular
                        </Link>
                        <Link href="/#" className="text-primary hover:underline">
                          Criar uma conta
                        </Link>
                      </div>
                    </CardFooter>
                  </form>
                </Form>
              </>
            ) : (
              // OTP Step UI
              <div className="flex h-full flex-col p-6">
                <CardHeader className="items-center px-0 pt-0">
                  <div className="mb-6 flex flex-col items-center gap-4">
                    <ShieldCheckIcon className="h-8 w-8 text-[#FDC201]" strokeWidth={2.5} />
                    <CardTitle className="text-center text-[22px] font-bold text-[#0F172A]">
                      Confirme seu código
                    </CardTitle>
                  </div>
                  <CardDescription className="w-full text-center text-[16px] font-normal leading-snug text-[#334155]">
                    Insira o código de 6 dígitos enviado para{' '}
                    <span className="font-semibold">{formattedPhoneForOTP || 'seu celular'}</span>{' '}
                    para confirmar seu acesso.
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-grow space-y-6 px-0">
                  <div className="flex w-full justify-center">
                    <OTPInput
                      value={otpValue}
                      onChange={setOtpValue}
                      maxLength={6}
                      disabled={isLoading}
                      error={!!currentError}
                    />
                  </div>
                  {currentError && (
                    <div className="w-full text-center text-[14px] font-medium text-[#FF3B3B]">
                      {currentError}
                    </div>
                  )}
                  <div className="w-full text-center">
                    <button
                      type="button"
                      onClick={handleResendCode}
                      className="text-[15px] font-semibold text-[#FDC201] hover:underline disabled:opacity-60"
                      disabled={isLoading}
                    >
                      Reenviar código
                    </button>
                  </div>
                </CardContent>
                <CardFooter className="mt-auto flex flex-col gap-3 px-0 pb-0">
                  <div className="flex w-full flex-col gap-3">
                    <Button
                      type="button"
                      className="flex h-12 w-full items-center justify-center rounded-xl bg-[#FDC201] text-[16px] font-bold text-[#0F172A] transition-opacity disabled:opacity-60"
                      onClick={handleVerifyOTP}
                      disabled={otpValue.length !== 6 || isLoading}
                    >
                      {isLoading && (
                        <Loader2 className="mr-2 h-5 w-5 animate-spin text-[#0F172A]" />
                      )}
                      Confirmar
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      className="flex h-12 w-full items-center justify-center rounded-xl border-[#FDC201] text-[16px] font-bold text-[#FDC201] transition-opacity hover:bg-inherit hover:text-[#FDC201] disabled:opacity-60"
                      onClick={handleBack}
                      disabled={isLoading}
                    >
                      <ArrowLeft className="mr-2 h-5 w-5" />
                      Voltar
                    </Button>
                  </div>
                  <Separator className="my-4" />
                  <div className="flex w-full flex-col items-start gap-2 text-sm">
                    <Link href="/#" className="text-primary hover:underline">
                      Mudei de número de celular
                    </Link>
                    <Link href="/#" className="text-primary hover:underline">
                      Criar uma conta
                    </Link>
                  </div>
                </CardFooter>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </Card>
    </div>
  );
}

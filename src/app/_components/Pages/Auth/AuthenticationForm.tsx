'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  ErrorDisplay,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  OTPInput,
  PhoneInput,
  Separator,
} from '@/src/app/_components';
import { useAuth } from '@/src/app/_context/AuthContext';
import { usePhoneVerification } from '@/src/app/_hooks';
import { cn, validatePhoneNumber } from '@/src/app/_utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { AnimatePresence, motion } from 'framer-motion';
import { ArrowLeft, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

// Form validation schema for phone step
const phoneVerificationSchema = z.object({
  phone: z
    .string()
    .min(1, 'Insira um número de telefone válido com DDD.')
    .refine(
      (value) => {
        const { isValid } = validatePhoneNumber(value, '+55');
        return isValid;
      },
      {
        message: 'Insira um número de telefone válido com DDD.',
      }
    ),
});

type PhoneVerificationFormValues = z.infer<typeof phoneVerificationSchema>;

interface AuthenticationFormProps {
  className?: string;
}

export function AuthenticationForm({ className }: AuthenticationFormProps) {
  const [step, setStep] = useState<'phone' | 'otp'>('phone');
  const router = useRouter();
  const {
    sendVerificationCode,
    verifyCode,
    isLoading: isPhoneVerificationLoading,
    error: phoneVerificationError,
    phoneNumber,
    resetVerification,
  } = usePhoneVerification();
  const { auth } = useAuth();

  const [phoneInputError, setPhoneInputError] = useState<string | null>(null);
  const [otpValue, setOtpValue] = useState('');
  const [formError, setFormError] = useState<string | null>(null); // Combined error state
  const [isResending, setIsResending] = useState(false); // Separate loading state for resend

  // Initialize form for phone step
  const phoneForm = useForm<PhoneVerificationFormValues>({
    resolver: zodResolver(phoneVerificationSchema),
    defaultValues: {
      phone: '',
    },
  });

  // Auto-validate OTP when all digits are filled
  useEffect(() => {
    if (step === 'otp' && otpValue.length === 6) {
      handleVerifyOTP();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [otpValue, step]);

  // Handle phone form submission
  const onSubmitPhone = async (values: PhoneVerificationFormValues) => {
    try {
      setFormError(null);
      setPhoneInputError(null);

      const validationResult = validatePhoneNumber(values.phone, '+55');
      if (!validationResult.isValid) {
        setPhoneInputError(validationResult.error);
        return;
      }

      const success = await sendVerificationCode(values.phone);
      if (success) {
        setStep('otp');
      }
    } catch (err) {
      console.error('Error submitting phone verification form:', err);
      setFormError(err instanceof Error ? err.message : 'Erro ao enviar código de verificação');
    }
  };

  // Handle OTP verification
  const handleVerifyOTP = async () => {
    if (otpValue.length === 6) {
      setFormError(null);
      const success = await verifyCode(otpValue);
      if (success) {
        router.push('/agendamentos');
      }
    }
  };

  // Handle resend code
  const handleResendCode = async () => {
    setIsResending(true);
    setFormError(null);
    setOtpValue('');

    try {
      if (phoneNumber) {
        // Attempt to resend code for the current phoneNumber
        // This assumes sendVerificationCode can be called again for resend
        const success = await sendVerificationCode(phoneNumber);
        if (!success) {
          setFormError('Erro ao reenviar o código. Tente novamente.');
        }
      } else {
        // If phoneNumber is somehow not available, prompt to go back
        setFormError('Número de telefone não encontrado. Por favor, volte e tente novamente.');
      }
    } finally {
      setIsResending(false);
    }
  };

  // Handle going back to phone verification step
  const handleBack = () => {
    setFormError(null);
    setStep('phone');
    setOtpValue('');
    resetVerification(); // Resets phoneNumber in usePhoneVerification hook
    phoneForm.reset(); // Reset the phone form as well
  };

  const currentError = formError || phoneVerificationError || phoneInputError;
  const isLoading = isPhoneVerificationLoading;

  // If user is already authenticated, show a message
  if (auth.isAuthenticated) {
    return (
      <div className={cn('mx-auto w-full max-w-md text-center', className)}>
        <h2 className="mb-4 text-xl font-bold">Você já está autenticado</h2>
        <p className="text-gray-600">Você já está logado no sistema.</p>
      </div>
    );
  }

  // Remove unused variable - not needed in simplified layout

  return (
    <div className={cn('mx-auto w-full max-w-md', className)}>
      <Card className="w-full p-8 shadow-xl">
        <AnimatePresence mode="wait">
          <motion.div
            key={step}
            initial={{ opacity: 0, x: step === 'phone' ? -20 : 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: step === 'phone' ? 20 : -20 }}
            transition={{ duration: 0.3 }}
            className="flex h-full flex-col"
          >
            {step === 'phone' ? (
              <>
                <Form {...phoneForm}>
                  <form
                    onSubmit={phoneForm.handleSubmit(onSubmitPhone)}
                    className="flex h-full flex-col"
                  >
                    <CardHeader>
                      <CardTitle className="text-4xl font-bold leading-tight">Entrar</CardTitle>
                      <CardDescription className="text-base">
                        Acesse seus agendamentos já realizados com seu número de celular.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-grow space-y-8">
                      <FormField
                        control={phoneForm.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={phoneInputError ? 'text-red-500' : ''}>
                              Celular
                            </FormLabel>
                            <FormControl>
                              <PhoneInput
                                value={field.value}
                                placeholder="(00) 00000-0000"
                                onChange={(value: string) => {
                                  field.onChange(value);
                                  // Clear both manual error and form errors on change
                                  setPhoneInputError(null);
                                  phoneForm.clearErrors('phone');
                                }}
                                onValidationError={setPhoneInputError} // For specific phone input errors
                                triggerValidation={() => phoneForm.trigger('phone')}
                              />
                            </FormControl>
                            {/* Display phone input error or form error, but not both */}
                            {phoneInputError ? (
                              <span className="mt-1 text-sm font-medium text-red-500">
                                {phoneInputError}
                              </span>
                            ) : (
                              <FormMessage />
                            )}
                          </FormItem>
                        )}
                      />
                      {currentError && !phoneInputError && (
                        <ErrorDisplay message={currentError} fullPage={false} />
                      )}
                    </CardContent>
                    <CardFooter className="mt-auto flex flex-col space-y-4">
                      <Button
                        type="submit"
                        className="h-12 w-full rounded-xl bg-[#0F172A] text-[16px] font-bold text-white transition-opacity hover:bg-[#0F172A]/90 disabled:opacity-60"
                        disabled={isLoading || !!phoneInputError || !phoneForm.formState.isValid}
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-5 w-5 animate-spin text-white" />
                            Enviando código...
                          </>
                        ) : (
                          'Enviar código de verificação'
                        )}
                      </Button>
                      <Separator />
                      <div className="mt-8 flex w-full flex-col items-start gap-2 text-sm">
                        <Link href="/#" className="text-primary hover:underline">
                          Mudei de número de celular
                        </Link>
                        <Link href="/#" className="text-primary hover:underline">
                          Criar uma conta
                        </Link>
                      </div>
                    </CardFooter>
                  </form>
                </Form>
              </>
            ) : (
              // OTP Step UI - Matching Figma Design Exactly
              <div className="flex h-full flex-col p-6">
                <CardHeader className="items-start px-0 pb-6 pt-0">
                  <Button
                    type="button"
                    variant="ghost"
                    className="mb-4 flex items-center gap-2 p-0 text-sm font-medium text-[#64748B] hover:bg-transparent"
                    onClick={handleBack}
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Voltar
                  </Button>
                  <CardTitle className="mb-2 text-left text-lg font-bold text-[#0F172A]">
                    Enviamos um código de verificação por SMS
                  </CardTitle>
                  <CardDescription className="text-left text-sm text-[#64748B]">
                    Código de verificação
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-grow space-y-6 px-0">
                  <div className="flex w-full justify-center">
                    <OTPInput
                      value={otpValue}
                      onChange={setOtpValue}
                      maxLength={6}
                      disabled={isLoading}
                      error={!!currentError}
                    />
                  </div>
                  {currentError && (
                    <div className="w-full text-center text-sm font-medium text-[#FF3B3B]">
                      {currentError}
                    </div>
                  )}
                </CardContent>
                <CardFooter className="mt-auto flex flex-col gap-3 px-0 pb-0">
                  <div className="flex w-full flex-col gap-3">
                    {/* Primary button - Entrar (on top) */}
                    <Button
                      type="button"
                      className="flex h-12 w-full items-center justify-center rounded-xl bg-[#0F172A] text-[16px] font-bold text-white transition-opacity hover:bg-[#0F172A]/90 disabled:opacity-60"
                      onClick={handleVerifyOTP}
                      disabled={otpValue.length !== 6 || isLoading}
                    >
                      {isLoading && <Loader2 className="mr-2 h-5 w-5 animate-spin text-white" />}
                      Entrar
                    </Button>

                    {/* Secondary button - Enviar código novamente (below) */}
                    <div className="w-full text-center">
                      <button
                        type="button"
                        onClick={handleResendCode}
                        className="text-[15px] font-semibold text-[#64748B] transition-opacity hover:underline disabled:opacity-60"
                        disabled={isLoading || isResending}
                      >
                        {isResending ? (
                          <span className="flex items-center justify-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            Reenviando...
                          </span>
                        ) : (
                          'Enviar código novamente'
                        )}
                      </button>
                    </div>
                  </div>
                  <Separator className="my-4" />
                  <div className="flex w-full flex-col items-start gap-2 text-sm">
                    <Link href="/#" className="text-primary hover:underline">
                      Preciso de ajuda
                    </Link>
                    <Link href="/#" className="text-primary hover:underline">
                      Criar uma conta
                    </Link>
                  </div>
                </CardFooter>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </Card>
    </div>
  );
}

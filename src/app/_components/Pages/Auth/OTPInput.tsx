'use client';

import { cn } from '@/src/app/_utils/utils';
import { OTPInput as InputOTP } from 'input-otp';
import { useRef } from 'react';

interface OTPInputProps {
  value: string;
  onChange: (value: string) => void;
  maxLength?: number;
  disabled?: boolean;
  error?: boolean;
  className?: string;
}

export function OTPInput({
  value,
  onChange,
  maxLength = 6,
  disabled = false,
  className,
}: OTPInputProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <div
      className={cn('flex w-full justify-center', className)}
      onClick={() => inputRef.current?.focus()}
    >
      <InputOTP
        ref={inputRef}
        maxLength={maxLength}
        value={value}
        onChange={onChange}
        disabled={disabled}
        containerClassName="flex items-center gap-3"
        className="group flex items-center"
        render={({ slots }) => {
          const firstEmptyIndex = slots.findIndex((slot) => !slot.char);
          return (
            <>
              {slots.map((slot, index) => {
                const isFilled = !!slot.char;
                const isFocused =
                  index === firstEmptyIndex ||
                  (firstEmptyIndex === -1 && index === slots.length - 1);
                return (
                  <div
                    key={index}
                    className={cn(
                      // Figma: 48x56px, 12px radius, border 1.5px #E2E8F0, bg #F8FAFC, Inter 20px 600 #0F172A, centered
                      'flex h-14 w-12 items-center justify-center rounded-[12px] border bg-[#F8FAFC] text-[20px] font-semibold text-[#0F172A] transition-all duration-200',
                      'focus-within:border-[#FDC201] focus-within:outline-none focus-within:ring-2 focus-within:ring-[#FDC201]',
                      isFilled && 'border-[#FDC201]',
                      isFocused &&
                        !isFilled &&
                        !disabled &&
                        'border-[#FDC201] ring-2 ring-[#FDC201]',
                      disabled && 'pointer-events-none opacity-40'
                    )}
                    style={{ borderWidth: '1.5px', fontFamily: 'Inter, sans-serif' }}
                  >
                    {slot.char ||
                      (index === firstEmptyIndex && !disabled ? (
                        <div className="h-8 w-0.5 animate-caret-blink rounded bg-[#FDC201]" />
                      ) : null)}
                  </div>
                );
              })}
            </>
          );
        }}
      />
    </div>
  );
}

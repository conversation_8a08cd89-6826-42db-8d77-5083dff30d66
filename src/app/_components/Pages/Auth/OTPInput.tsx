'use client';

import { cn } from '@/src/app/_utils/utils';
import { OTPInput as InputOTP } from 'input-otp';
import { useRef } from 'react';

interface OTPInputProps {
  value: string;
  onChange: (value: string) => void;
  maxLength?: number;
  disabled?: boolean;
  error?: boolean;
  className?: string;
}

export function OTPInput({
  value,
  onChange,
  maxLength = 6,
  disabled = false,
  error = false,
  className,
}: OTPInputProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <div
      className={cn('flex w-full justify-center', className)}
      onClick={() => inputRef.current?.focus()}
    >
      <InputOTP
        ref={inputRef}
        maxLength={maxLength}
        value={value}
        onChange={onChange}
        disabled={disabled}
        containerClassName="flex items-center gap-3"
        className="group flex items-center"
        render={({ slots }) => {
          const firstEmptyIndex = slots.findIndex((slot) => !slot.char);
          return (
            <>
              {slots.map((slot, index) => {
                const isFilled = !!slot.char;
                const isFocused =
                  index === firstEmptyIndex ||
                  (firstEmptyIndex === -1 && index === slots.length - 1);

                return (
                  <div
                    key={index}
                    className={cn(
                      // Base styling matching Figma exactly: 48x56px, 12px radius, Inter font
                      'flex h-14 w-12 items-center justify-center rounded-xl border-[1.5px] bg-[#F8FAFC] text-xl font-semibold text-[#0F172A] transition-all duration-200',
                      // Default border color
                      'border-[#E2E8F0]',
                      // Focus states
                      'focus-within:border-[#FDC201] focus-within:outline-none focus-within:ring-2 focus-within:ring-[#FDC201]/20',
                      // Filled state
                      isFilled && 'border-[#FDC201] bg-[#FDC201]/5',
                      // Active focus state for empty field
                      isFocused &&
                        !isFilled &&
                        !disabled &&
                        'border-[#FDC201] ring-2 ring-[#FDC201]/20',
                      // Error state
                      error && 'border-[#FF3B3B] bg-[#FF3B3B]/5',
                      // Disabled state
                      disabled && 'pointer-events-none bg-[#F1F5F9] opacity-40'
                    )}
                    style={{
                      fontFamily: 'Inter, system-ui, -apple-system, sans-serif',
                      fontSize: '20px',
                      lineHeight: '1',
                    }}
                  >
                    {slot.char ||
                      (index === firstEmptyIndex && !disabled ? (
                        <div
                          className="h-6 w-0.5 rounded-full bg-[#FDC201]"
                          style={{
                            animation: 'caret-blink 1s ease-in-out infinite'
                          }}
                        />
                      ) : null)}
                  </div>
                );
              })}
            </>
          );
        }}
      />
    </div>
  );
}

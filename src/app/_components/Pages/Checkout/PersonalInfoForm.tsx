'use client';

import { PhoneInput } from '@/src/app/_components/Common/PhoneInput';
import { UserIcon as UserFilled } from 'lucide-react';
import { useEffect, useState } from 'react';
import ReactCountryFlag from 'react-country-flag';
import { useFormContext } from 'react-hook-form';

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
} from '@/src/app/_components';

import { useInputFormat } from '@/src/app/_hooks';

import { PersonalInfoFormProps } from '@/src/app/_interfaces';
import { CheckoutFormSchema, cn, countries, Country } from '@/src/app/_utils';

export function PersonalInfoForm({ focusedBlock, setFocusedBlock }: PersonalInfoFormProps) {
  const form = useFormContext<CheckoutFormSchema>();
  const [phoneInputError, setPhoneInputError] = useState<string | null>(null);

  const { capitalizeWords, formatCpf } = useInputFormat();

  // UseEffect para tirar o blur dos inputs ao realizar o scroll
  useEffect(() => {
    const handleScroll = () => {
      if (document.activeElement instanceof HTMLElement) {
        document.activeElement.blur(); // Remove o foco do input ativo
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Simplified focus handling
  const handleFocus = () => {
    if (typeof setFocusedBlock === 'function') {
      setFocusedBlock('personal');
    }
  };

  const handleBlur = () => {
    if (typeof setFocusedBlock === 'function') {
      setFocusedBlock(null);
    }
  };

  const phonePlaceholders: { [key: string]: string } = {
    '+55': '(00) 00000-0000',
    '+1': '(*************',
    '+54': '(00) 0000-0000',
    '+56': '0 0000 0000',
    '+57': '************',
    '+52': '(00) 0000-0000',
    '+51': '000 000 000',
    '+598': '0000 0000',
    '+33': '00 00 00 00 00',
    '+49': '0000 0000000',
    '+34': '000 000 000',
    '+39': '************',
    '+351': '000 000 000',
  };

  return (
    <Card
      className={cn(
        'transition-shadow duration-300',
        focusedBlock === 'personal' && 'shadow-lg ring-2 ring-primary ring-opacity-50'
      )}
    >
      <CardHeader className="px-8">
        <div className="flex flex-col gap-2 md:flex-row md:items-center">
          <UserFilled className="h-5 w-5 flex-shrink-0 text-primary" strokeWidth={3} />
          <CardTitle className="text-lg font-bold leading-tight">
            Quais suas informações para contato?
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 px-8 pb-8">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Seu nome"
                    {...field}
                    required
                    onFocus={handleFocus}
                    onBlur={(e) => {
                      field.onChange(e.target.value);
                      handleBlur();
                      form.trigger('firstName');
                    }}
                    onChange={(e) => {
                      const capitalizedValue = capitalizeWords(e.target.value);
                      field.onChange(capitalizedValue);
                      form.trigger('firstName');
                    }}
                    className={cn(form.formState.errors.firstName && 'input-error')}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Sobrenome</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Seu sobrenome"
                    {...field}
                    required
                    onFocus={handleFocus}
                    onBlur={(e) => {
                      field.onChange(e.target.value);
                      handleBlur();
                      form.trigger('lastName');
                    }}
                    onChange={(e) => {
                      const capitalizedValue = capitalizeWords(e.target.value);
                      field.onChange(capitalizedValue);
                      form.trigger('lastName');
                    }}
                    className={cn(form.formState.errors.lastName && 'input-error')}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-12 gap-2">
          <FormField
            control={form.control}
            name="countryCode"
            render={({ field }) => {
              // Always use Brazil's country code
              if (!field.value || field.value !== '+55') {
                setTimeout(() => {
                  field.onChange('+55');
                }, 0);
              }

              // Find Brazil in the countries array
              const brazilCountry = countries.find((c: Country) => c.code === '+55');

              return (
                <FormItem className="col-span-6 md:col-span-2">
                  <FormLabel>País</FormLabel>
                  <div className="flex h-10 w-full items-center rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background">
                    {brazilCountry && (
                      <span className="flex items-center">
                        <ReactCountryFlag
                          countryCode={brazilCountry.iso}
                          svg
                          style={{
                            width: '1.5em',
                            height: '1.5em',
                            marginRight: '0.5em',
                          }}
                        />
                        {brazilCountry.code}
                      </span>
                    )}
                  </div>
                  <input type="hidden" {...field} value="+55" />
                  <FormMessage />
                </FormItem>
              );
            }}
          />
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem className="col-span-12 md:col-span-10">
                <FormLabel className={cn(phoneInputError && 'text-red-500')}>Celular</FormLabel>
                <FormControl>
                  <PhoneInput
                    value={field.value || ''}
                    placeholder={phonePlaceholders['+55'] || '(00) 00000-0000'}
                    onChange={(value: string) => {
                      field.onChange(value);
                      form.trigger('phone');
                    }}
                    onValidationError={setPhoneInputError}
                    useFormatPhoneNumber={true}
                    triggerValidation={() => form.trigger('phone')}
                    gridColumnClasses="col-span-12 md:col-span-10"
                    showCountryCode={false}
                  />
                </FormControl>
                {!phoneInputError && <FormMessage />}
              </FormItem>
            )}
          />
          <span className="col-span-12 text-sm text-muted-foreground">
            O prestador de serviço irá entrar em contato através desse número.
          </span>
        </div>

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...field}
                  value={field.value || ''}
                  required
                  onFocus={handleFocus}
                  onBlur={(e) => {
                    field.onChange(e.target.value);
                    handleBlur();
                    form.trigger('email');
                  }}
                  onChange={(e) => {
                    field.onChange(e.target.value.trim());
                    form.trigger('email');
                  }}
                  className={cn(form.formState.errors.email && 'input-error')}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="cpf"
          render={({ field }) => (
            <FormItem>
              <FormLabel>CPF</FormLabel>
              <FormControl>
                <Input
                  placeholder="000.000.000-00"
                  {...field}
                  value={field.value || ''}
                  required
                  onFocus={handleFocus}
                  onBlur={(e) => {
                    field.onChange(e.target.value);
                    handleBlur();
                    form.trigger('cpf');
                  }}
                  onChange={(e) => {
                    const formattedValue = formatCpf(e.target.value);
                    field.onChange(formattedValue);
                    form.trigger('cpf');
                  }}
                  className={cn(form.formState.errors.cpf && 'input-error')}
                  aria-invalid={!!form.formState.errors.cpf}
                  aria-describedby={
                    form.formState.errors.cpf ? `cpf-error-${field.name}` : undefined
                  }
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}
